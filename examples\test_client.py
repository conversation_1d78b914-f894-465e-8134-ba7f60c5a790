"""
MCP Client Test Suite

This script provides comprehensive testing for the Python MCP client
to validate functionality against the running Knock MCP server.

Run this script to verify that the client works correctly with the server.
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List

from mcp_client import MCPClient, MCPError, MCPConnectionError, MCPProtocolError


# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise during testing
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCPClientTester:
    """Test suite for MCP client functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000/mcp"):
        self.base_url = base_url
        self.client = None
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result."""
        status = "PASS" if success else "FAIL"
        self.test_results.append({
            "test": test_name,
            "status": status,
            "message": message
        })
        print(f"[{status}] {test_name}: {message}")
    
    async def test_connection(self) -> bool:
        """Test basic connection to MCP server."""
        try:
            self.client = MCPClient(self.base_url)
            connection_info = await self.client.connect()
            
            # Verify connection info
            if not connection_info.get("serverInfo"):
                self.log_test("Connection", False, "No server info returned")
                return False
            
            if not connection_info.get("capabilities"):
                self.log_test("Connection", False, "No capabilities returned")
                return False
            
            self.log_test("Connection", True, f"Connected to {connection_info['serverInfo'].get('name', 'Unknown')}")
            return True
            
        except Exception as e:
            self.log_test("Connection", False, str(e))
            return False
    
    async def test_ping(self) -> bool:
        """Test ping functionality."""
        try:
            result = await self.client.ping()
            self.log_test("Ping", result, "Server responded" if result else "No response")
            return result
        except Exception as e:
            self.log_test("Ping", False, str(e))
            return False
    
    async def test_list_tools(self) -> bool:
        """Test tool listing."""
        try:
            tools = await self.client.list_tools()
            
            if not tools:
                self.log_test("List Tools", False, "No tools returned")
                return False
            
            # Verify expected tools exist
            expected_tools = [
                "pricing_and_availability",
                "get_knock_admin_property_info",
                "schedule_property_tour",
                "update_prospect_guestcard"
            ]
            
            tool_names = [tool.name for tool in tools]
            missing_tools = [name for name in expected_tools if name not in tool_names]
            
            if missing_tools:
                self.log_test("List Tools", False, f"Missing tools: {missing_tools}")
                return False
            
            self.log_test("List Tools", True, f"Found {len(tools)} tools including all expected ones")
            return True
            
        except Exception as e:
            self.log_test("List Tools", False, str(e))
            return False
    
    async def test_pricing_tool(self) -> bool:
        """Test pricing and availability tool."""
        try:
            result = await self.client.call_tool(
                "pricing_and_availability",
                {
                    "property_id": 123,
                    "bedrooms": "2",
                    "move_date": "2025-08-01"
                }
            )
            
            # Verify result structure
            if not isinstance(result, dict):
                self.log_test("Pricing Tool", False, "Result is not a dictionary")
                return False
            
            # Check for expected fields
            expected_fields = ["availability", "pricing_link", "requested_bedrooms"]
            missing_fields = [field for field in expected_fields if field not in result]
            
            if missing_fields:
                self.log_test("Pricing Tool", False, f"Missing fields: {missing_fields}")
                return False
            
            self.log_test("Pricing Tool", True, f"Availability: {result.get('availability')}")
            return True
            
        except Exception as e:
            self.log_test("Pricing Tool", False, str(e))
            return False
    
    async def test_property_info_tool(self) -> bool:
        """Test property info tool."""
        try:
            result = await self.client.call_tool(
                "get_knock_admin_property_info",
                {"property_id": 456}
            )
            
            if not isinstance(result, dict):
                self.log_test("Property Info Tool", False, "Result is not a dictionary")
                return False
            
            self.log_test("Property Info Tool", True, f"Retrieved info with {len(result)} fields")
            return True
            
        except Exception as e:
            self.log_test("Property Info Tool", False, str(e))
            return False
    
    async def test_tour_tool(self) -> bool:
        """Test tour scheduling tool."""
        try:
            result = await self.client.call_tool(
                "schedule_property_tour",
                {
                    "property_id": 789,
                    "renter_id": 101,
                    "tour_date": "2025-08-15T14:00:00",
                    "first_name": "Test",
                    "last_name": "User"
                }
            )
            
            if not isinstance(result, dict):
                self.log_test("Tour Tool", False, "Result is not a dictionary")
                return False
            
            # Check for expected fields
            if "status" not in result:
                self.log_test("Tour Tool", False, "Missing status field")
                return False
            
            self.log_test("Tour Tool", True, f"Status: {result.get('status')}")
            return True
            
        except Exception as e:
            self.log_test("Tour Tool", False, str(e))
            return False
    
    async def test_prospect_tool(self) -> bool:
        """Test prospect guest card tool."""
        try:
            result = await self.client.call_tool(
                "update_prospect_guestcard",
                {
                    "prospect_id": 12345,
                    "request": {
                        "profile": {
                            "first_name": "Test",
                            "last_name": "User",
                            "email": "<EMAIL>"
                        },
                        "preferences": {
                            "bedrooms": 2,
                            "pet_friendly": False
                        }
                    }
                }
            )
            
            if not isinstance(result, dict):
                self.log_test("Prospect Tool", False, "Result is not a dictionary")
                return False
            
            self.log_test("Prospect Tool", True, "Guest card updated successfully")
            return True
            
        except Exception as e:
            self.log_test("Prospect Tool", False, str(e))
            return False
    
    async def test_error_handling(self) -> bool:
        """Test error handling scenarios."""
        try:
            # Test invalid tool name
            try:
                await self.client.call_tool("nonexistent_tool", {})
                self.log_test("Error Handling", False, "Expected error for invalid tool but call succeeded")
                return False
            except MCPError:
                pass  # Expected
            
            # Test invalid parameters
            try:
                await self.client.call_tool("pricing_and_availability", {"invalid_param": "value"})
                self.log_test("Error Handling", False, "Expected error for invalid params but call succeeded")
                return False
            except MCPError:
                pass  # Expected
            
            self.log_test("Error Handling", True, "Correctly handled error scenarios")
            return True
            
        except Exception as e:
            self.log_test("Error Handling", False, str(e))
            return False
    
    async def test_concurrent_calls(self) -> bool:
        """Test concurrent tool calls."""
        try:
            # Create multiple concurrent calls
            tasks = []
            for i in range(3):
                task = self.client.call_tool(
                    "pricing_and_availability",
                    {"property_id": 100 + i, "bedrooms": "1"}
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check results
            successful = sum(1 for r in results if not isinstance(r, Exception))
            
            if successful < len(tasks):
                self.log_test("Concurrent Calls", False, f"Only {successful}/{len(tasks)} calls succeeded")
                return False
            
            self.log_test("Concurrent Calls", True, f"All {len(tasks)} concurrent calls succeeded")
            return True
            
        except Exception as e:
            self.log_test("Concurrent Calls", False, str(e))
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return results."""
        print("Starting MCP Client Test Suite")
        print("=" * 50)
        
        tests = [
            ("Connection", self.test_connection),
            ("Ping", self.test_ping),
            ("List Tools", self.test_list_tools),
            ("Pricing Tool", self.test_pricing_tool),
            ("Property Info Tool", self.test_property_info_tool),
            ("Tour Tool", self.test_tour_tool),
            ("Prospect Tool", self.test_prospect_tool),
            ("Error Handling", self.test_error_handling),
            ("Concurrent Calls", self.test_concurrent_calls),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                success = await test_func()
                if success:
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log_test(test_name, False, f"Test crashed: {e}")
                failed += 1
        
        # Cleanup
        if self.client:
            await self.client.disconnect()
        
        # Summary
        print("\n" + "=" * 50)
        print("Test Results Summary")
        print("=" * 50)
        print(f"Total tests: {passed + failed}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success rate: {(passed / (passed + failed) * 100):.1f}%")
        
        if failed > 0:
            print("\nFailed tests:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['message']}")
        
        return {
            "total": passed + failed,
            "passed": passed,
            "failed": failed,
            "success_rate": passed / (passed + failed) * 100,
            "results": self.test_results
        }


async def main():
    """Run the test suite."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000/mcp"
    
    print(f"Testing MCP client against: {base_url}")
    print("Make sure the Knock MCP server is running!")
    print()
    
    tester = MCPClientTester(base_url)
    results = await tester.run_all_tests()
    
    # Exit with error code if tests failed
    if results["failed"] > 0:
        sys.exit(1)
    else:
        print("\n🎉 All tests passed!")
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
