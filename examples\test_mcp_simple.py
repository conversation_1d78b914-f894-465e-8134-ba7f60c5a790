"""Simple test of the MCP client."""

import asyncio
import logging
from mcp_client import MC<PERSON>lient, MCPError

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

async def test_simple():
    """Test basic MCP client functionality."""
    client = MCPClient("http://localhost:8000/mcp")
    
    try:
        print("1. Connecting...")
        result = await client.connect()
        print(f"Connected! Server: {result}")
        
        print("\n2. Listing tools...")
        tools = await client.list_tools()
        print(f"Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        if tools:
            print("\n3. Testing a tool...")
            result = await client.call_tool(
                "pricing_and_availability",
                {"property_id": 123, "bedrooms": "2"}
            )
            print(f"Tool result: {result}")
        
    except MCPError as e:
        print(f"MCP Error: {e}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_simple())
