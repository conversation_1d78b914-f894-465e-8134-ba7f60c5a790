"""Explore the MCP endpoints to understand the protocol."""

import asyncio
import httpx
import json


async def explore_mcp_endpoints():
    """Explore what endpoints FastMCP creates."""
    async with httpx.AsyncClient() as client:
        base_url = "http://localhost:8000"
        
        print("Exploring MCP endpoints...")
        
        # Try different potential endpoints
        endpoints_to_try = [
            "/mcp",
            "/mcp/",
            "/mcp/message",
            "/mcp/sse",
            "/mcp/tools",
            "/mcp/initialize",
            "/mcp/capabilities"
        ]
        
        for endpoint in endpoints_to_try:
            try:
                print(f"\nTrying GET {endpoint}...")
                resp = await client.get(f"{base_url}{endpoint}")
                print(f"  Status: {resp.status_code}")
                if resp.status_code < 400:
                    try:
                        data = resp.json()
                        print(f"  Response: {json.dumps(data, indent=2)[:200]}...")
                    except:
                        print(f"  Response: {resp.text[:200]}...")
                        
            except Exception as e:
                print(f"  Error: {e}")
        
        # Try POST to /mcp/message with MCP initialize
        print(f"\nTrying POST /mcp/message with initialize...")
        try:
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {},
                        "resources": {},
                        "prompts": {}
                    },
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            resp = await client.post(
                f"{base_url}/mcp/message",
                json=init_message,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream"
                }
            )
            print(f"  Status: {resp.status_code}")
            print(f"  Content-Type: {resp.headers.get('content-type')}")
            if resp.status_code < 400:
                try:
                    data = resp.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response (text): {resp.text[:500]}...")
            else:
                print(f"  Error response: {resp.text}")
                
        except Exception as e:
            print(f"  Error: {e}")


if __name__ == "__main__":
    asyncio.run(explore_mcp_endpoints())
