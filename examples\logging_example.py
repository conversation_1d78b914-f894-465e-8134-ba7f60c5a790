"""
Example tool demonstrating comprehensive logging patterns for the Knock MCP Server.

This module shows different ways to use logging in your tools, including:
- Different log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Structured logging with context
- Performance logging
- Error handling with logging
- Conditional logging
"""

import time

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from src.utils.log_config import get_logger

router = APIRouter()
logger = get_logger(__name__)


class ExampleRequest(BaseModel):
    user_id: int
    action: str
    data: dict | None = None


class ExampleResponse(BaseModel):
    success: bool
    message: str
    data: dict | None = None


@router.post(
    "/example",
    operation_id="example_logging_tool",
    response_model=ExampleResponse,
)
async def example_logging_tool(request: ExampleRequest) -> ExampleResponse:
    """
    Example tool demonstrating comprehensive logging patterns.
    """
    # 1. Basic logging with context
    logger.info(f"Processing request for user_id: {request.user_id}, action: {request.action}")

    # 2. Debug logging for detailed information
    logger.debug(f"Request data: {request.data}")

    # 3. Performance logging
    start_time = time.time()

    try:
        # 4. Conditional logging based on data
        if request.data and len(request.data) > 10:
            logger.warning(f"Large data payload detected: {len(request.data)} items")

        # 5. Simulate some processing
        logger.info("Starting data processing...")

        # Simulate different scenarios
        if request.action == "create":
            logger.info("Creating new resource...")
            result = {"id": 123, "status": "created"}

        elif request.action == "update":
            logger.info("Updating existing resource...")
            result = {"id": request.user_id, "status": "updated"}

        elif request.action == "delete":
            logger.warning(f"Deleting resource for user_id: {request.user_id}")
            result = {"id": request.user_id, "status": "deleted"}

        else:
            logger.error(f"Unknown action: {request.action}")
            raise HTTPException(status_code=400, detail=f"Unknown action: {request.action}")

        # 6. Performance logging
        processing_time = time.time() - start_time
        logger.info(f"Processing completed in {processing_time:.3f} seconds")

        # 7. Success logging
        logger.info(f"Successfully processed {request.action} for user_id: {request.user_id}")

        return ExampleResponse(success=True, message=f"Successfully processed {request.action}", data=result)

    except HTTPException:
        # 8. Re-raise HTTP exceptions without additional logging
        raise

    except Exception as e:
        # 9. Error logging with exception details
        logger.error(f"Unexpected error processing request: {e!s}")
        logger.exception("Full exception details:")

        # 10. Performance logging even on error
        processing_time = time.time() - start_time
        logger.error(f"Request failed after {processing_time:.3f} seconds")

        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/health",
    operation_id="health_check_with_logging",
)
async def health_check_with_logging():
    """
    Health check endpoint with logging.
    """
    logger.debug("Health check requested")

    # Simulate some health checks
    checks = {"database": "healthy", "cache": "healthy", "external_api": "healthy"}

    # Log each health check
    for service, status in checks.items():
        if status == "healthy":
            logger.debug(f"Health check passed for {service}")
        else:
            logger.warning(f"Health check failed for {service}: {status}")

    logger.info("Health check completed successfully")
    return {"status": "healthy", "checks": checks}


# Example of a utility function with logging
def process_data_with_logging(data: dict, user_id: int) -> dict:
    """
    Example utility function showing logging patterns.
    """
    logger.debug(f"Processing data for user_id: {user_id}")

    try:
        # Validate data
        if not data:
            logger.warning(f"Empty data received for user_id: {user_id}")
            return {}

        # Process data
        processed_data = {}
        for key, value in data.items():
            logger.debug(f"Processing key: {key}")
            processed_data[key] = str(value).upper()

        logger.info(f"Successfully processed {len(data)} items for user_id: {user_id}")
        return processed_data

    except Exception as e:
        logger.error(f"Error processing data for user_id {user_id}: {e!s}")
        logger.exception("Data processing exception:")
        raise


# Example of conditional logging based on log level
def conditional_logging_example():
    """
    Shows how to use conditional logging to avoid expensive operations.
    """
    # Only execute expensive operation if debug logging is enabled
    if logger.isEnabledFor(10):  # DEBUG level
        expensive_data = "This is expensive to compute"
        logger.debug(f"Expensive data: {expensive_data}")

    # Always log important information
    logger.info("Important operation completed")
