"""
Python MCP (Model Context Protocol) Client Implementation

This module provides a complete MCP client implementation that can connect to
MCP servers via HTTP transport and perform all major MCP operations including:
- Server capability discovery
- Tool listing and invocation
- Resource listing and reading
- Prompt listing and execution
- Error handling and connection management

Example usage:
    client = MCPClient("http://localhost:8000/mcp")
    await client.connect()
    tools = await client.list_tools()
    result = await client.call_tool("pricing_and_availability", {"property_id": 123, "bedrooms": "2"})
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

import httpx
from pydantic import BaseModel, ValidationError


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MCPMessageType(str, Enum):
    """MCP message types according to the protocol specification."""
    INITIALIZE = "initialize"
    INITIALIZED = "initialized"
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"
    LIST_PROMPTS = "prompts/list"
    GET_PROMPT = "prompts/get"
    PING = "ping"
    PONG = "pong"


@dataclass
class MCPCapabilities:
    """MCP server capabilities."""
    tools: Optional[Dict[str, Any]] = None
    resources: Optional[Dict[str, Any]] = None
    prompts: Optional[Dict[str, Any]] = None
    logging: Optional[Dict[str, Any]] = None


class MCPMessage(BaseModel):
    """Base MCP message structure."""
    jsonrpc: str = "2.0"
    id: Optional[Union[str, int]] = None
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None


class MCPTool(BaseModel):
    """MCP tool definition."""
    name: str
    description: Optional[str] = None
    inputSchema: Optional[Dict[str, Any]] = None


class MCPResource(BaseModel):
    """MCP resource definition."""
    uri: str
    name: Optional[str] = None
    description: Optional[str] = None
    mimeType: Optional[str] = None


class MCPPrompt(BaseModel):
    """MCP prompt definition."""
    name: str
    description: Optional[str] = None
    arguments: Optional[List[Dict[str, Any]]] = None


class MCPError(Exception):
    """Base exception for MCP-related errors."""
    def __init__(self, message: str, code: Optional[int] = None, data: Optional[Any] = None):
        super().__init__(message)
        self.code = code
        self.data = data


class MCPConnectionError(MCPError):
    """Exception raised when connection to MCP server fails."""
    pass


class MCPProtocolError(MCPError):
    """Exception raised when MCP protocol errors occur."""
    pass


class MCPClient:
    """
    Python MCP client implementation with HTTP transport.
    
    This client provides a complete implementation of the MCP protocol
    for communicating with MCP servers over HTTP.
    """
    
    def __init__(self, base_url: str, timeout: float = 30.0):
        """
        Initialize the MCP client.
        
        Args:
            base_url: Base URL of the MCP server (e.g., "http://localhost:8000/mcp")
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.AsyncClient(timeout=timeout)
        self.capabilities: Optional[MCPCapabilities] = None
        self.server_info: Optional[Dict[str, Any]] = None
        self.connected = False
        self._message_id = 0
        
        logger.info(f"Initialized MCP client for {self.base_url}")
    
    def _get_next_id(self) -> int:
        """Get the next message ID."""
        self._message_id += 1
        return self._message_id
    
    async def _send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send an MCP request to the server.

        Args:
            method: MCP method name
            params: Request parameters

        Returns:
            Response data

        Raises:
            MCPConnectionError: If connection fails
            MCPProtocolError: If protocol error occurs
        """
        message = MCPMessage(
            id=self._get_next_id(),
            method=method,
            params=params or {}
        )

        try:
            logger.debug(f"Sending MCP request: {method}")
            response = await self.client.post(
                f"{self.base_url}/message",
                json=message.model_dump(exclude_none=True),
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream"
                }
            )
            response.raise_for_status()

            # Handle SSE response format
            response_text = response.text
            logger.debug(f"Received raw response: {repr(response_text)}")

            # Parse SSE format: "event: message\ndata: {json}\n\n"
            if "event: message" in response_text and "data: " in response_text:
                # Extract JSON from SSE format
                lines = response_text.split('\n')
                json_data = None
                for line in lines:
                    if line.startswith('data: '):
                        json_data = line[6:]  # Remove "data: " prefix
                        break

                if json_data:
                    response_data = json.loads(json_data)
                else:
                    raise MCPProtocolError("No data found in SSE response")
            else:
                # Fallback to direct JSON parsing
                response_data = response.json()

            logger.debug(f"Parsed MCP response: {response_data}")

            # Check for MCP protocol errors
            if "error" in response_data:
                error = response_data["error"]
                raise MCPProtocolError(
                    error.get("message", "Unknown MCP error"),
                    code=error.get("code"),
                    data=error.get("data")
                )

            return response_data.get("result", {})

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during MCP request: {e}")
            raise MCPConnectionError(f"Failed to send MCP request: {e}") from e
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON response: {e}")
            raise MCPProtocolError(f"Invalid JSON response: {e}") from e

    async def _send_notification(self, method: str, params: Optional[Dict[str, Any]] = None) -> None:
        """
        Send an MCP notification to the server (no response expected).

        Args:
            method: MCP method name
            params: Request parameters

        Raises:
            MCPConnectionError: If connection fails
        """
        message = MCPMessage(
            method=method,
            params=params or {}
        )

        try:
            logger.debug(f"Sending MCP notification: {method}")
            response = await self.client.post(
                f"{self.base_url}/message",
                json=message.model_dump(exclude_none=True),
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream"
                }
            )
            response.raise_for_status()
            logger.debug(f"Notification {method} sent successfully")

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during MCP notification: {e}")
            raise MCPConnectionError(f"Failed to send MCP notification: {e}") from e
    
    async def connect(self) -> Dict[str, Any]:
        """
        Connect to the MCP server and perform initialization handshake.
        
        Returns:
            Server information and capabilities
            
        Raises:
            MCPConnectionError: If connection fails
        """
        try:
            logger.info("Connecting to MCP server...")
            
            # Send initialize request
            init_params = {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {},
                    "prompts": {}
                },
                "clientInfo": {
                    "name": "python-mcp-client",
                    "version": "1.0.0"
                }
            }
            
            result = await self._send_request(MCPMessageType.INITIALIZE, init_params)
            
            # Store server info and capabilities
            self.server_info = result.get("serverInfo", {})
            server_capabilities = result.get("capabilities", {})
            self.capabilities = MCPCapabilities(
                tools=server_capabilities.get("tools"),
                resources=server_capabilities.get("resources"),
                prompts=server_capabilities.get("prompts"),
                logging=server_capabilities.get("logging")
            )
            
            # Send initialized notification (no response expected)
            await self._send_notification(MCPMessageType.INITIALIZED)
            
            self.connected = True
            logger.info(f"Successfully connected to MCP server: {self.server_info.get('name', 'Unknown')}")
            
            return {
                "serverInfo": self.server_info,
                "capabilities": server_capabilities
            }
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            raise MCPConnectionError(f"Connection failed: {e}") from e
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        if self.client:
            await self.client.aclose()
        self.connected = False
        logger.info("Disconnected from MCP server")
    
    async def ping(self) -> bool:
        """
        Send a ping to the server to check connectivity.

        Returns:
            True if server responds with pong
        """
        try:
            await self._send_request(MCPMessageType.PING)
            return True
        except Exception as e:
            logger.warning(f"Ping failed: {e}")
            return False

    # Tool Operations

    async def list_tools(self) -> List[MCPTool]:
        """
        List all available tools from the MCP server.

        Returns:
            List of available tools

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info("Listing available tools...")
            result = await self._send_request(MCPMessageType.LIST_TOOLS)

            tools = []
            for tool_data in result.get("tools", []):
                try:
                    tool = MCPTool(**tool_data)
                    tools.append(tool)
                except ValidationError as e:
                    logger.warning(f"Invalid tool data: {e}")

            logger.info(f"Found {len(tools)} tools")
            return tools

        except Exception as e:
            logger.error(f"Failed to list tools: {e}")
            raise

    async def call_tool(self, name: str, arguments: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Call a tool on the MCP server.

        Args:
            name: Tool name
            arguments: Tool arguments

        Returns:
            Tool execution result

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info(f"Calling tool: {name}")
            params = {
                "name": name,
                "arguments": arguments or {}
            }

            result = await self._send_request(MCPMessageType.CALL_TOOL, params)
            logger.info(f"Tool {name} executed successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to call tool {name}: {e}")
            raise

    # Resource Operations

    async def list_resources(self) -> List[MCPResource]:
        """
        List all available resources from the MCP server.

        Returns:
            List of available resources

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info("Listing available resources...")
            result = await self._send_request(MCPMessageType.LIST_RESOURCES)

            resources = []
            for resource_data in result.get("resources", []):
                try:
                    resource = MCPResource(**resource_data)
                    resources.append(resource)
                except ValidationError as e:
                    logger.warning(f"Invalid resource data: {e}")

            logger.info(f"Found {len(resources)} resources")
            return resources

        except Exception as e:
            logger.error(f"Failed to list resources: {e}")
            raise

    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """
        Read a resource from the MCP server.

        Args:
            uri: Resource URI

        Returns:
            Resource content

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info(f"Reading resource: {uri}")
            params = {"uri": uri}

            result = await self._send_request(MCPMessageType.READ_RESOURCE, params)
            logger.info(f"Resource {uri} read successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to read resource {uri}: {e}")
            raise

    # Prompt Operations

    async def list_prompts(self) -> List[MCPPrompt]:
        """
        List all available prompts from the MCP server.

        Returns:
            List of available prompts

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info("Listing available prompts...")
            result = await self._send_request(MCPMessageType.LIST_PROMPTS)

            prompts = []
            for prompt_data in result.get("prompts", []):
                try:
                    prompt = MCPPrompt(**prompt_data)
                    prompts.append(prompt)
                except ValidationError as e:
                    logger.warning(f"Invalid prompt data: {e}")

            logger.info(f"Found {len(prompts)} prompts")
            return prompts

        except Exception as e:
            logger.error(f"Failed to list prompts: {e}")
            raise

    async def get_prompt(self, name: str, arguments: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get a prompt from the MCP server.

        Args:
            name: Prompt name
            arguments: Prompt arguments

        Returns:
            Prompt content

        Raises:
            MCPConnectionError: If not connected to server
            MCPProtocolError: If protocol error occurs
        """
        if not self.connected:
            raise MCPConnectionError("Not connected to MCP server")

        try:
            logger.info(f"Getting prompt: {name}")
            params = {
                "name": name,
                "arguments": arguments or {}
            }

            result = await self._send_request(MCPMessageType.GET_PROMPT, params)
            logger.info(f"Prompt {name} retrieved successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to get prompt {name}: {e}")
            raise

    # Utility Methods

    def get_server_info(self) -> Optional[Dict[str, Any]]:
        """Get server information."""
        return self.server_info

    def get_capabilities(self) -> Optional[MCPCapabilities]:
        """Get server capabilities."""
        return self.capabilities

    def is_connected(self) -> bool:
        """Check if connected to server."""
        return self.connected

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):  # noqa: ARG002
        """Async context manager exit."""
        await self.disconnect()
        return None
