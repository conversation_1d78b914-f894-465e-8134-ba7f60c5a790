"""Simple test to check if the server is running."""

import asyncio
import httpx


async def test_server():
    """Test if the server is running."""
    async with httpx.AsyncClient() as client:
        try:
            # Test health endpoint
            resp = await client.get('http://localhost:8000/api/health')
            print(f"Health check: {resp.status_code} {resp.json()}")
            
            # Test MCP endpoint
            resp = await client.get('http://localhost:8000/mcp')
            print(f"MCP endpoint: {resp.status_code}")
            
            return True
        except Exception as e:
            print(f"Error: {e}")
            return False


if __name__ == "__main__":
    asyncio.run(test_server())
