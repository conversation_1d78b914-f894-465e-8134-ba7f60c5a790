# Python MCP Client Requirements
# 
# These are the minimal dependencies needed to run the MCP client examples.
# If you're using the main project environment (uv sync), these should already be available.

# HTTP client for MCP communication
httpx>=0.28.1

# Data validation and serialization
pydantic>=2.6.0

# Async support (included in Python 3.11+ but listed for clarity)
# asyncio is part of the standard library

# Optional: Enhanced logging (if not using standard library)
# structlog>=23.1.0

# Development/Testing dependencies (optional)
# pytest>=8.0.0
# pytest-asyncio>=0.21.0
