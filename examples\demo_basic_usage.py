"""
Basic MCP Client Usage Demo

This script demonstrates basic usage of the Python MCP client to connect
to the Knock MCP server and perform common operations.

Run this script with the MCP server running on localhost:8000
"""

import asyncio
import json
import logging
from typing import Any, Dict

from mcp_client import MCPClient, MCPError


# Configure logging for demo
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def demo_basic_connection():
    """Demonstrate basic connection and server discovery."""
    print("\n" + "="*60)
    print("DEMO: Basic Connection and Server Discovery")
    print("="*60)
    
    client = MCPClient("http://localhost:8000/mcp")
    
    try:
        # Connect to server
        print("\n1. Connecting to MCP server...")
        connection_info = await client.connect()
        
        print(f"✓ Connected successfully!")
        print(f"  Server: {connection_info['serverInfo'].get('name', 'Unknown')}")
        print(f"  Version: {connection_info['serverInfo'].get('version', 'Unknown')}")
        
        # Test ping
        print("\n2. Testing server connectivity...")
        ping_result = await client.ping()
        print(f"✓ Ping successful: {ping_result}")
        
        # Get capabilities
        print("\n3. Server capabilities:")
        capabilities = client.get_capabilities()
        if capabilities:
            print(f"  Tools: {'✓' if capabilities.tools else '✗'}")
            print(f"  Resources: {'✓' if capabilities.resources else '✗'}")
            print(f"  Prompts: {'✓' if capabilities.prompts else '✗'}")
            print(f"  Logging: {'✓' if capabilities.logging else '✗'}")
        
    except MCPError as e:
        print(f"✗ MCP Error: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
    finally:
        await client.disconnect()


async def demo_tool_operations():
    """Demonstrate tool listing and invocation."""
    print("\n" + "="*60)
    print("DEMO: Tool Operations")
    print("="*60)
    
    async with MCPClient("http://localhost:8000/mcp") as client:
        try:
            # List available tools
            print("\n1. Listing available tools...")
            tools = await client.list_tools()
            
            print(f"✓ Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description or 'No description'}")
            
            if not tools:
                print("  No tools available")
                return
            
            # Demonstrate pricing and availability tool
            print("\n2. Testing pricing_and_availability tool...")
            try:
                result = await client.call_tool(
                    "pricing_and_availability",
                    {
                        "property_id": 123,
                        "bedrooms": "2",
                        "move_date": "2025-08-01"
                    }
                )
                print("✓ Tool executed successfully!")
                print(f"  Result: {json.dumps(result, indent=2)}")
                
            except MCPError as e:
                print(f"✗ Tool execution failed: {e}")
            
            # Demonstrate property info tool
            print("\n3. Testing get_knock_admin_property_info tool...")
            try:
                result = await client.call_tool(
                    "get_knock_admin_property_info",
                    {"property_id": 456}
                )
                print("✓ Tool executed successfully!")
                print(f"  Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                
            except MCPError as e:
                print(f"✗ Tool execution failed: {e}")
            
            # Demonstrate tour scheduling tool
            print("\n4. Testing schedule_property_tour tool...")
            try:
                result = await client.call_tool(
                    "schedule_property_tour",
                    {
                        "property_id": 789,
                        "renter_id": 101,
                        "tour_date": "2025-08-15T14:00:00",
                        "first_name": "John",
                        "last_name": "Doe",
                        "preference_bedrooms": 2,
                        "preference_move_date": "2025-09-01"
                    }
                )
                print("✓ Tool executed successfully!")
                print(f"  Result: {json.dumps(result, indent=2)}")
                
            except MCPError as e:
                print(f"✗ Tool execution failed: {e}")
                
        except MCPError as e:
            print(f"✗ MCP Error: {e}")
        except Exception as e:
            print(f"✗ Unexpected error: {e}")


async def demo_prospect_operations():
    """Demonstrate prospect/guest card operations."""
    print("\n" + "="*60)
    print("DEMO: Prospect Operations")
    print("="*60)
    
    async with MCPClient("http://localhost:8000/mcp") as client:
        try:
            print("\n1. Testing update_prospect_guestcard tool...")
            
            # Create sample guest card data
            guest_card_data = {
                "prospect_id": 12345,
                "request": {
                    "profile": {
                        "first_name": "Jane",
                        "last_name": "Smith",
                        "email": "<EMAIL>",
                        "phone": "555-0123",
                        "move_in_date": "2025-09-01",
                        "lease_term": 12,
                        "budget_min": 1200,
                        "budget_max": 1800
                    },
                    "preferences": {
                        "bedrooms": 2,
                        "bathrooms": 2,
                        "pet_friendly": True,
                        "parking": True,
                        "amenities": ["gym", "pool", "laundry"]
                    }
                }
            }
            
            result = await client.call_tool(
                "update_prospect_guestcard",
                guest_card_data
            )
            print("✓ Guest card updated successfully!")
            print(f"  Result: {json.dumps(result, indent=2)}")
            
        except MCPError as e:
            print(f"✗ MCP Error: {e}")
        except Exception as e:
            print(f"✗ Unexpected error: {e}")


async def demo_error_handling():
    """Demonstrate error handling scenarios."""
    print("\n" + "="*60)
    print("DEMO: Error Handling")
    print("="*60)
    
    async with MCPClient("http://localhost:8000/mcp") as client:
        try:
            print("\n1. Testing invalid tool call...")
            try:
                await client.call_tool("nonexistent_tool", {})
                print("✗ Expected error but call succeeded")
            except MCPError as e:
                print(f"✓ Correctly caught MCP error: {e}")
            
            print("\n2. Testing tool with invalid parameters...")
            try:
                await client.call_tool(
                    "pricing_and_availability",
                    {"invalid_param": "value"}
                )
                print("✗ Expected error but call succeeded")
            except MCPError as e:
                print(f"✓ Correctly caught parameter error: {e}")
            
            print("\n3. Testing tool with missing required parameters...")
            try:
                await client.call_tool("pricing_and_availability", {})
                print("✗ Expected error but call succeeded")
            except MCPError as e:
                print(f"✓ Correctly caught missing parameter error: {e}")
                
        except Exception as e:
            print(f"✗ Unexpected error: {e}")


async def main():
    """Run all demo scenarios."""
    print("Python MCP Client Demo")
    print("Make sure the Knock MCP server is running on http://localhost:8000")
    print("Start the server with: uv run python main.py")
    
    try:
        await demo_basic_connection()
        await demo_tool_operations()
        await demo_prospect_operations()
        await demo_error_handling()
        
        print("\n" + "="*60)
        print("DEMO COMPLETED")
        print("="*60)
        print("All demos completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\n\nDemo failed with error: {e}")
        logger.exception("Demo failed")


if __name__ == "__main__":
    asyncio.run(main())
