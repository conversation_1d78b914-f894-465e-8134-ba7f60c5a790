"""
Advanced MCP Client Usage Demo

This script demonstrates advanced usage patterns of the Python MCP client
including batch operations, error recovery, and real-world workflows.

Run this script with the MCP server running on localhost:8000
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List

from mcp_client import MCPClient, MCPError, MCPConnectionError, MCPProtocolError


# Configure logging for demo
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PropertySearchWorkflow:
    """Demonstrates a real-world property search workflow using MCP tools."""
    
    def __init__(self, client: MCPClient):
        self.client = client
    
    async def search_properties(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Search for properties based on criteria using multiple MCP tools.
        
        Args:
            criteria: Search criteria including bedrooms, budget, move_date, etc.
            
        Returns:
            List of matching properties with availability and pricing
        """
        print(f"\n🔍 Searching properties with criteria: {criteria}")
        
        # Sample property IDs to check (in real scenario, these would come from a search API)
        property_ids = [123, 456, 789, 101112]
        matching_properties = []
        
        for property_id in property_ids:
            try:
                print(f"  Checking property {property_id}...")
                
                # Get property information
                property_info = await self.client.call_tool(
                    "get_knock_admin_property_info",
                    {"property_id": property_id}
                )
                
                # Check pricing and availability
                availability = await self.client.call_tool(
                    "pricing_and_availability",
                    {
                        "property_id": property_id,
                        "bedrooms": str(criteria.get("bedrooms", 2)),
                        "move_date": criteria.get("move_date")
                    }
                )
                
                # Filter based on criteria
                if self._matches_criteria(availability, criteria):
                    matching_properties.append({
                        "property_id": property_id,
                        "property_info": property_info,
                        "availability": availability
                    })
                    print(f"    ✓ Property {property_id} matches criteria")
                else:
                    print(f"    ✗ Property {property_id} doesn't match criteria")
                
                # Add small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)
                
            except MCPError as e:
                print(f"    ✗ Error checking property {property_id}: {e}")
                continue
        
        print(f"✓ Found {len(matching_properties)} matching properties")
        return matching_properties
    
    def _matches_criteria(self, availability: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """Check if property availability matches search criteria."""
        # Check availability status
        if availability.get("availability") != "matched":
            return False
        
        # Check price range
        price = availability.get("availability_price")
        if price:
            try:
                price_val = float(price)
                min_budget = criteria.get("min_budget", 0)
                max_budget = criteria.get("max_budget", float('inf'))
                if not (min_budget <= price_val <= max_budget):
                    return False
            except (ValueError, TypeError):
                pass
        
        return True
    
    async def schedule_tours(self, properties: List[Dict[str, Any]], renter_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Schedule tours for multiple properties."""
        print(f"\n📅 Scheduling tours for {len(properties)} properties")
        
        scheduled_tours = []
        base_date = "2025-08-15T"
        times = ["10:00:00", "14:00:00", "16:00:00"]
        
        for i, property_data in enumerate(properties[:3]):  # Limit to 3 tours
            try:
                tour_time = base_date + times[i % len(times)]
                
                result = await self.client.call_tool(
                    "schedule_property_tour",
                    {
                        "property_id": property_data["property_id"],
                        "renter_id": renter_info["renter_id"],
                        "tour_date": tour_time,
                        "first_name": renter_info["first_name"],
                        "last_name": renter_info["last_name"],
                        "preference_bedrooms": renter_info.get("preference_bedrooms"),
                        "preference_move_date": renter_info.get("preference_move_date")
                    }
                )
                
                scheduled_tours.append({
                    "property_id": property_data["property_id"],
                    "tour_result": result,
                    "tour_time": tour_time
                })
                
                print(f"  ✓ Scheduled tour for property {property_data['property_id']} at {tour_time}")
                
            except MCPError as e:
                print(f"  ✗ Failed to schedule tour for property {property_data['property_id']}: {e}")
        
        return scheduled_tours


async def demo_batch_operations():
    """Demonstrate batch operations with error handling."""
    print("\n" + "="*60)
    print("DEMO: Batch Operations")
    print("="*60)
    
    async with MCPClient("http://localhost:8000/mcp") as client:
        try:
            # Batch check multiple properties
            property_ids = [123, 456, 789, 999, 1001]
            results = []
            
            print(f"\n1. Batch checking {len(property_ids)} properties...")
            
            # Use asyncio.gather for concurrent requests (be careful with rate limits)
            tasks = []
            for prop_id in property_ids:
                task = client.call_tool(
                    "pricing_and_availability",
                    {"property_id": prop_id, "bedrooms": "2"}
                )
                tasks.append(task)
            
            # Execute with timeout and error handling
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=30.0
                )
                
                successful = 0
                failed = 0
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        print(f"  ✗ Property {property_ids[i]}: {result}")
                        failed += 1
                    else:
                        print(f"  ✓ Property {property_ids[i]}: {result.get('availability', 'unknown')}")
                        successful += 1
                
                print(f"\nBatch results: {successful} successful, {failed} failed")
                
            except asyncio.TimeoutError:
                print("✗ Batch operation timed out")
                
        except Exception as e:
            print(f"✗ Batch operation failed: {e}")


async def demo_workflow():
    """Demonstrate a complete property search and booking workflow."""
    print("\n" + "="*60)
    print("DEMO: Complete Property Search Workflow")
    print("="*60)
    
    async with MCPClient("http://localhost:8000/mcp") as client:
        try:
            workflow = PropertySearchWorkflow(client)
            
            # Define search criteria
            search_criteria = {
                "bedrooms": 2,
                "min_budget": 1000,
                "max_budget": 2000,
                "move_date": "2025-09-01"
            }
            
            # Define renter information
            renter_info = {
                "renter_id": 12345,
                "first_name": "Alice",
                "last_name": "Johnson",
                "preference_bedrooms": 2,
                "preference_move_date": "2025-09-01"
            }
            
            # Step 1: Search for properties
            matching_properties = await workflow.search_properties(search_criteria)
            
            if not matching_properties:
                print("No matching properties found")
                return
            
            # Step 2: Schedule tours
            scheduled_tours = await workflow.schedule_tours(matching_properties, renter_info)
            
            # Step 3: Create/update prospect guest card
            print(f"\n📝 Creating prospect guest card...")
            try:
                guest_card_result = await client.call_tool(
                    "update_prospect_guestcard",
                    {
                        "prospect_id": renter_info["renter_id"],
                        "request": {
                            "profile": {
                                "first_name": renter_info["first_name"],
                                "last_name": renter_info["last_name"],
                                "email": "<EMAIL>",
                                "phone": "555-0199",
                                "move_in_date": search_criteria["move_date"],
                                "budget_min": search_criteria["min_budget"],
                                "budget_max": search_criteria["max_budget"]
                            },
                            "preferences": {
                                "bedrooms": search_criteria["bedrooms"],
                                "pet_friendly": False,
                                "parking": True
                            }
                        }
                    }
                )
                print("✓ Guest card created/updated successfully")
                
            except MCPError as e:
                print(f"✗ Failed to create guest card: {e}")
            
            # Summary
            print(f"\n📊 Workflow Summary:")
            print(f"  Properties searched: Multiple")
            print(f"  Properties matched: {len(matching_properties)}")
            print(f"  Tours scheduled: {len(scheduled_tours)}")
            print(f"  Guest card: {'✓' if 'guest_card_result' in locals() else '✗'}")
            
        except Exception as e:
            print(f"✗ Workflow failed: {e}")


async def demo_connection_resilience():
    """Demonstrate connection resilience and error recovery."""
    print("\n" + "="*60)
    print("DEMO: Connection Resilience")
    print("="*60)
    
    client = MCPClient("http://localhost:8000/mcp", timeout=5.0)
    
    try:
        print("\n1. Testing connection with short timeout...")
        await client.connect()
        print("✓ Connected successfully")
        
        print("\n2. Testing ping with connectivity check...")
        for i in range(3):
            ping_result = await client.ping()
            print(f"  Ping {i+1}: {'✓' if ping_result else '✗'}")
            await asyncio.sleep(1)
        
        print("\n3. Testing rapid successive calls...")
        start_time = time.time()
        
        tasks = []
        for i in range(5):
            task = client.call_tool(
                "pricing_and_availability",
                {"property_id": 100 + i, "bedrooms": "1"}
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        elapsed = time.time() - start_time
        successful = sum(1 for r in results if not isinstance(r, Exception))
        
        print(f"  Completed {successful}/5 calls in {elapsed:.2f}s")
        
    except MCPConnectionError as e:
        print(f"✗ Connection error: {e}")
    except MCPProtocolError as e:
        print(f"✗ Protocol error: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
    finally:
        await client.disconnect()


async def main():
    """Run all advanced demo scenarios."""
    print("Advanced Python MCP Client Demo")
    print("Make sure the Knock MCP server is running on http://localhost:8000")
    
    try:
        await demo_batch_operations()
        await demo_workflow()
        await demo_connection_resilience()
        
        print("\n" + "="*60)
        print("ADVANCED DEMO COMPLETED")
        print("="*60)
        print("All advanced demos completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\n\nDemo failed with error: {e}")
        logger.exception("Advanced demo failed")


if __name__ == "__main__":
    asyncio.run(main())
